import { IMessagePackage, MessageStatus, Role } from "@/types";

/**
 * Message 构造函数配置接口
 */
export interface MessageConfig {
  /** 消息的唯一标识符 */
  id: string;
  /** 消息发送者的角色 */
  role: Role;
  /** 组成消息的内容包数组 */
  content?: IMessagePackage[];
  /** 消息的状态 */
  status?: MessageStatus;
  /** AgentCode */
  agentCode: string;
  /** 是否为最新消息 */
  isFresh?: boolean;
  /** 用户评分 */
  user_rating?: "like" | "dislike";
}

export enum MessageErrorType {
  Terminated = "TERMINATED",
}

interface MessageError {
  message: string;
  type: MessageErrorType;
}

/**
 * 消息类
 *
 * 表示聊天系统中的一条消息，包含消息内容、状态、发送者等信息
 *
 * @example
 * ```typescript
 * const message = new Message({
 *   id: "msg-123",
 *   role: Role.HUMAN,
 *   agentCode: "assistant",
 *   content: [textPackage]
 * });
 * ```
 */
export class Message {
  /** 消息的唯一标识符 */
  readonly id: string;
  /** 组成消息的内容包数组 */
  content: IMessagePackage[];
  /** 消息发送者的角色 */
  readonly role: Role;
  /** 消息的状态 */
  status: MessageStatus;
  /** AgentCode */
  readonly agentCode: string;
  /** 是否为最新消息 */
  isFresh: boolean;
  /** 用户评分 */
  user_rating?: "like" | "dislike";
  /** 错误信息 */
  error?: MessageError;

  /**
   * 构造函数
   * @param config 消息配置对象
   */
  constructor(config: MessageConfig) {
    // 输入验证
    if (!config.id?.trim()) {
      throw new Error("Message id is required and cannot be empty");
    }
    if (!config.agentCode?.trim()) {
      throw new Error("Message agentCode is required and cannot be empty");
    }

    this.id = config.id.trim();
    this.role = config.role;
    this.content = config.content ?? [];
    this.status = config.status ?? MessageStatus.Loading;
    this.agentCode = config.agentCode.trim();
    this.isFresh = config.isFresh ?? false;
    this.user_rating = config.user_rating;
  }

  /**
   * 添加内容包到消息中
   * @param messagePackage 要添加的消息包
   */
  addContent(messagePackage: IMessagePackage): void {
    this.content.push(messagePackage);
  }

  /**
   * 移除指定的内容包
   * @param packageId 要移除的包ID
   * @returns 是否成功移除
   */
  removeContent(packageId: number): boolean {
    const index = this.content.findIndex((pkg) => pkg.package_id === packageId);
    if (index !== -1) {
      this.content.splice(index, 1);
      return true;
    }
    return false;
  }

  /**
   * 获取指定ID的内容包
   * @param packageId 包ID
   * @returns 找到的内容包或undefined
   */
  getContent(packageId: number): IMessagePackage | undefined {
    return this.content.find((pkg) => pkg.package_id === packageId);
  }

  /**
   * 检查消息是否正在加载
   * @returns 是否正在加载
   */
  isLoading(): boolean {
    return this.status === MessageStatus.Loading;
  }

  /**
   * 检查消息是否已完成
   * @returns 是否已完成
   */
  isFinished(): boolean {
    return this.status === MessageStatus.Finished;
  }

  /**
   * 检查消息是否出错
   * @returns 是否出错
   */
  isError(): boolean {
    return this.status === MessageStatus.Error;
  }

  /**
   * 检查是否为人类消息
   * @returns 是否为人类消息
   */
  isHuman(): boolean {
    return this.role === Role.HUMAN;
  }

  /**
   * 检查是否为AI消息
   * @returns 是否为AI消息
   */
  isAI(): boolean {
    return this.role === Role.AI;
  }

  /**
   * 获取消息的纯文本内容
   * @returns 纯文本内容
   */
  getTextContent(): string {
    return this.content
      .filter((pkg) => pkg.package_type === 0) // MessagePackageType.Text
      .map((pkg) => pkg.data)
      .join("");
  }

  /**
   * 标记为最新消息
   */
  markAsFresh(): void {
    this.isFresh = true;
  }

  /**
   * 取消最新消息标记
   */
  unmarkAsFresh(): void {
    this.isFresh = false;
  }

  /**
   * 设置用户评分
   * @param rating 评分
   */
  setRating(rating: "like" | "dislike" | undefined): void {
    this.user_rating = rating;
  }

  /**
   * 更新消息状态
   * @param status 新状态
   */
  updateStatus(status: MessageStatus): void {
    this.status = status;
  }

  /**
   * 设置错误信息
   * @param error 错误信息
   */
  setError(error: MessageError): void {
    this.status = MessageStatus.Error;
    this.error = error;
  }

  /**
   * 获取错误信息
   * @returns 错误信息
   */
  getErrorMessage(): string | undefined {
    return this.error?.message;
  }
}

/**
 * 创建 Message 实例的工厂函数（兼容旧接口）
 * @param id 消息的唯一标识符
 * @param role 消息发送者的角色
 * @param content 组成消息的内容包数组
 * @param status 消息的状态
 * @param agentCode AgentCode
 * @param user_rating 用户评分（可选）
 * @returns Message 实例
 */
export function createMessage(
  id: string,
  role: Role,
  content: IMessagePackage[],
  status: MessageStatus = MessageStatus.Loading,
  agentCode: string,
  user_rating?: "like" | "dislike",
): Message {
  return new Message({
    id,
    role,
    content,
    status,
    agentCode,
    user_rating,
  });
}

/**
 * 创建 Message 实例的工厂函数（使用配置对象）
 * @param config 消息配置对象
 * @returns Message 实例
 */
export function createMessageFromConfig(config: MessageConfig): Message {
  return new Message(config);
}

/**
 * 创建人类消息的便利函数
 * @param id 消息ID
 * @param content 消息内容包数组
 * @param agentCode 智能体代码
 * @returns Message 实例
 */
export function createHumanMessage(id: string, content: IMessagePackage[], agentCode: string): Message {
  return new Message({
    id,
    role: Role.HUMAN,
    content,
    status: MessageStatus.Finished,
    agentCode,
    isFresh: false,
  });
}

/**
 * 创建AI消息的便利函数
 * @param id 消息ID
 * @param agentCode 智能体代码
 * @param content 消息内容包数组（可选）
 * @returns Message 实例
 */
export function createAIMessage(id: string, agentCode: string, content?: IMessagePackage[]): Message {
  return new Message({
    id,
    role: Role.AI,
    content: content ?? [],
    status: MessageStatus.Loading,
    agentCode,
    isFresh: true,
  });
}
