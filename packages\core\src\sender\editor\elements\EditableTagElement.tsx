import React from "react";
import { RenderElementProps, useFocused, useSelected } from "slate-react";

// EditableTag Element Component
export const EditableTagElement: React.FC<RenderElementProps> = ({ attributes, children, element }) => {
  const selected = useSelected();
  const focused = useFocused();
  const { placeholder } = element as any;

  // Check if the element has content
  const hasContent = element.children.some((child: any) => child.text && child.text.trim() !== "");

  return (
    <span
      {...attributes}
      style={{
        display: "inline-block",
        margin: "0 4px",
        padding: "4px 8px",
        borderRadius: "6px",
        backgroundColor: "rgba(11,104,230,0.1)",
        color: "#9013FE",
        border: selected && focused ? "2px solid #0B68E6" : "none",
        position: "relative",
      }}
    >
      {!hasContent && placeholder && (
        <span
          contentEditable={false}
          style={{
            color: "rgba(0,0,0,0.35)",
            position: "absolute",
            pointerEvents: "none",
          }}
        >
          {placeholder}
        </span>
      )}
      {children}
    </span>
  );
};

export default EditableTagElement;
