import { useDebounceFn } from "ahooks";
import mermaid from "mermaid";
import React, { useEffect, useRef } from "react";
import Markdown from "react-markdown";
import rehypeHighlight from "rehype-highlight";
import rehypeRaw from "rehype-raw";
import remarkGfm from "remark-gfm";

mermaid.initialize({
  startOnLoad: false,
  theme: "default",
  securityLevel: "loose",
  suppressErrorRendering: true,
});

interface TextBlockProps {
  content: string;
}

interface MermaidRendererProps {
  code: string;
}

const MermaidRenderer: React.FC<MermaidRendererProps> = ({ code }) => {
  const [diagram, setDiagram] = React.useState<string | null>(null);
  const id = useRef(`mermaid-${Math.random().toString(36).substring(2, 11)}`);

  const { run: renderMermaid } = useDebounceFn(
    () => {
      mermaid
        .parse(code, { suppressErrors: true })
        .then((result) => {
          if (!result) {
            setDiagram(null);
            return;
          }
          return mermaid.render(id.current, code);
        })
        .then((result) => {
          if (result) {
            setDiagram(result.svg);
          }
        });
    },
    { wait: 100 },
  );

  useEffect(() => {
    renderMermaid();
  }, [code]);

  return diagram ? (
    <div className="ag:flex ag:justify-center ag:my-4">
      <div
        dangerouslySetInnerHTML={{ __html: diagram ?? "" }}
        className="ag:max-w-full ag:overflow-auto"
        style={{ lineHeight: "normal" }}
      />
    </div>
  ) : (
    <pre>
      ```mermaid
      <br />
      {code}
      <br />
      ```
    </pre>
  );
};

const Code: React.FC<any> = (props) => {
  const { className, children, ...rest } = props;
  const match = /language-(\w+)/.exec(className || "");
  const language = match ? match[1] : "";
  const code = String(children).replace(/\n$/, "");

  return language === "mermaid" ? (
    <MermaidRenderer code={code} />
  ) : (
    <code className={className} {...rest}>
      {children}
    </code>
  );
};

const components = {
  code: Code,
};
const rehypePlugins = [rehypeHighlight, rehypeRaw];
const remarkPlugins = [remarkGfm];

const TextBlock: React.FC<TextBlockProps> = (props) => {
  const { content } = props;

  return (
    <div className="cscs-agent-text-block ag:break-all ag:leading-[1.6em]">
      <Markdown rehypePlugins={rehypePlugins} remarkPlugins={remarkPlugins} components={components}>
        {content}
      </Markdown>
    </div>
  );
};

export default TextBlock;
