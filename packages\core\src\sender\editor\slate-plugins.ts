import { Editor, Node, Path, Element as SlateElement, Transforms } from "slate";

// Plugin to handle custom elements
export const withCustomElements = (editor: Editor) => {
  const { isInline, isVoid, normalizeNode } = editor;

  // Define which elements are inline
  editor.isInline = (element: SlateElement) => {
    return ["tag", "editable-tag", "select"].includes(element.type) ? true : isInline(element);
  };

  // Define which elements are void (cannot contain text)
  editor.isVoid = (element: SlateElement) => {
    return ["tag", "select"].includes(element.type) ? true : isVoid(element);
  };

  // Normalize nodes to ensure proper structure
  editor.normalizeNode = (entry: [Node, Path]) => {
    const [node, path] = entry;

    // If it's an element node
    if (SlateElement.isElement(node)) {
      // Ensure tag elements have proper structure
      if (node.type === "tag") {
        // Tag elements should be void and have empty text children
        if (node.children.length !== 1 || node.children[0].text !== "") {
          Transforms.removeNodes(editor, { at: path.concat([0]) });
          Transforms.insertNodes(editor, { text: "" }, { at: path.concat([0]) });
          return;
        }
      }

      // Ensure select elements have proper structure
      if (node.type === "select") {
        // Select elements should be void and have empty text children
        if (node.children.length !== 1 || node.children[0].text !== "") {
          Transforms.removeNodes(editor, { at: path.concat([0]) });
          Transforms.insertNodes(editor, { text: "" }, { at: path.concat([0]) });
          return;
        }
      }

      // Ensure editable-tag elements have at least one text child
      if (node.type === "editable-tag") {
        if (node.children.length === 0) {
          Transforms.insertNodes(editor, { text: "" }, { at: path.concat([0]) });
          return;
        }
      }

      // Ensure paragraph elements have at least one text child
      if (node.type === "paragraph") {
        if (node.children.length === 0) {
          Transforms.insertNodes(editor, { text: "" }, { at: path.concat([0]) });
          return;
        }
      }
    }

    // Fall back to the original `normalizeNode` to enforce other constraints
    normalizeNode(entry);
  };

  return editor;
};

// Helper functions for inserting custom elements
export const insertTag = (editor: Editor, text: string, rawValue: string, tooltips?: string) => {
  const tagElement = {
    type: "tag" as const,
    text,
    rawValue,
    tooltips,
    children: [{ text: "" }],
  };

  Transforms.insertNodes(editor, tagElement);
};

export const insertEditableTag = (editor: Editor, placeholder?: string) => {
  const editableTagElement = {
    type: "editable-tag" as const,
    placeholder,
    children: [{ text: "" }],
  };

  Transforms.insertNodes(editor, editableTagElement);
};

export const insertSelect = (
  editor: Editor,
  options: Array<{ label: string; value: string }>,
  placeholder?: string,
  defaultValue?: string,
  tooltips?: string,
  disabled?: boolean,
  apiConfig?: any,
) => {
  const selectElement = {
    type: "select" as const,
    placeholder,
    options,
    defaultValue,
    tooltips,
    disabled,
    apiConfig,
    children: [{ text: "" }],
  };

  Transforms.insertNodes(editor, selectElement);
};
