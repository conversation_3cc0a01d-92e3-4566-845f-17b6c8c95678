import { Tooltip } from "antd";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { RenderElementProps, useFocused, useSelected } from "slate-react";

import { get, post } from "@/request";

// API Configuration Interface
export interface ApiConfig {
  url: string;
  method?: "GET" | "POST";
  headers?: Record<string, string>;
  params?: Record<string, unknown>;
  data?: Record<string, unknown>;
  mapper?: {
    label: string;
    value: string;
  };
}

interface ApiOptionResponse {
  data?: { label: string; value: string }[];
  [key: string]: unknown;
}

// Select Element Component
export const SelectElement: React.FC<RenderElementProps> = ({ attributes, children, element }) => {
  const selected = useSelected();
  const focused = useFocused();

  const { placeholder, options: staticOptions, apiConfig, defaultValue, tooltips, disabled } = element as any;

  const [selectedValue, setSelectedValue] = useState<string | undefined>(defaultValue);
  const [apiOptions, setApiOptions] = useState<{ label: string; value: unknown }[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Merge static and API options
  const allOptions = [...(staticOptions || []), ...apiOptions];

  // Fetch options from API
  const fetchOptionsFromApi = useCallback(async () => {
    if (!apiConfig) return;

    try {
      setLoading(true);
      setError(null);

      const controller = new AbortController();
      abortControllerRef.current = controller;

      const { url, method = "GET", headers, params, data } = apiConfig;

      let response;
      const requestOptions = {
        signal: controller.signal,
        headers,
      };

      switch (method.toUpperCase()) {
        case "GET":
          response = await get<ApiOptionResponse>(url, params, requestOptions);
          break;
        case "POST":
          response = await post<ApiOptionResponse>(url, data, { ...requestOptions, params });
          break;
        default:
          throw new Error(`Unsupported HTTP method: ${method}`);
      }

      const responseData = response?.data.data;
      let options: { label: string; value: unknown }[] = [];

      if (Array.isArray(responseData)) {
        if (apiConfig.mapper) {
          options = responseData.map((item: any) => {
            const labelKey = apiConfig.mapper?.label;
            const valueKey = apiConfig.mapper?.value;
            if (labelKey !== undefined && valueKey !== undefined) {
              return { label: item[labelKey], value: item[valueKey] };
            }
            return item;
          });
        } else {
          options = responseData;
        }
      }

      setApiOptions(options);
    } catch (err: unknown) {
      if ((err as Error).name === "AbortError" || (err as Error).message === "canceled") {
        return;
      }
      console.error("Failed to fetch options from API:", err);
      setError((err as Error).message || "Failed to fetch options");
    } finally {
      setLoading(false);
      abortControllerRef.current = null;
    }
  }, [apiConfig]);

  const handleChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const value = event.target.value;
    setSelectedValue(value);
    // Note: In a real implementation, you might want to update the Slate element's attributes
  };

  useEffect(() => {
    fetchOptionsFromApi();

    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [fetchOptionsFromApi]);

  const selectElement = (
    <select
      value={selectedValue}
      onChange={handleChange}
      disabled={disabled || loading}
      style={{
        minWidth: "120px",
        fontSize: "14px",
        padding: "2px 4px",
        border: "1px solid #d9d9d9",
        borderRadius: "4px",
        backgroundColor: loading ? "#f5f5f5" : "white",
      }}
      contentEditable={false}
    >
      <option value="" disabled>
        {loading ? "Loading..." : placeholder || "Please select..."}
      </option>
      {allOptions.map((option, index) => (
        <option key={index} value={option.value as string}>
          {option.label}
        </option>
      ))}
      {error && (
        <option value="" disabled>
          Error: {error}
        </option>
      )}
    </select>
  );

  return (
    <span
      {...attributes}
      contentEditable={false}
      style={{
        display: "inline-block",
        margin: "0 4px",
        border: selected && focused ? "2px solid #0B68E6" : "none",
        borderRadius: "4px",
      }}
    >
      {tooltips ? <Tooltip title={tooltips}>{selectElement}</Tooltip> : selectElement}
      {children}
    </span>
  );
};

export default SelectElement;
