import React, { forwardRef, useC<PERSON>back, useEffect, useImperative<PERSON><PERSON><PERSON>, useMemo, useState } from "react";
import { Descendant, Editor as SlateEditor, Element as SlateElement, Text, Transforms, createEditor } from "slate";
import { withHistory } from "slate-history";
import { Editable, ReactEditor, Slate, withReact } from "slate-react";

import { EditableTagElement, SelectElement, TagElement } from "./elements";
import { withCustomElements } from "./slate-plugins";

interface EditorProps {
  onChange: (value: string) => void;
  onEnterPress?: (event: KeyboardEvent) => void;
  ref?: React.Ref<EditorRef>;
}

export interface EditorRef {
  focus: () => void;
  clear: () => void;
  insertText: (text: string) => void;
  getText: () => string;
}

// Define custom element types for Slate
declare module "slate" {
  interface CustomTypes {
    Editor: SlateEditor & ReactEditor;
    Element: {
      type: "paragraph" | "tag" | "editable-tag" | "select";
      children: Descendant[];
      // Tag attributes
      text?: string;
      rawValue?: string;
      tooltips?: string;
      // EditableTag attributes
      placeholder?: string;
      // Select attributes
      options?: Array<{ label: string; value: string }>;
      apiConfig?: any;
      defaultValue?: string;
      disabled?: boolean;
    };
    Text: {
      text: string;
      bold?: boolean;
      italic?: boolean;
      underline?: boolean;
    };
  }
}

const Editor = forwardRef<EditorRef, EditorProps>(function Editor(props, ref) {
  const { onChange, onEnterPress } = props;

  // Create Slate editor with plugins
  const editor = useMemo(() => withCustomElements(withHistory(withReact(createEditor()))), []);

  // Initial value for the editor
  const [value, setValue] = useState<Descendant[]>([
    {
      type: "paragraph",
      children: [{ text: "" }],
    },
  ]);

  // Convert Slate value to HTML string
  const serializeToHTML = useCallback((nodes: Descendant[]): string => {
    return nodes
      .map((node) => {
        if (SlateElement.isElement(node)) {
          switch (node.type) {
            case "tag":
              return `<embedded-tag text="${node.text || ""}" rawValue="${node.rawValue || ""}" tooltips="${node.tooltips || ""}"></embedded-tag>`;
            case "editable-tag":
              const content = node.children.map((child) => (Text.isText(child) ? child.text : "")).join("");
              return `<embedded-editable-tag placeholder="${node.placeholder || ""}">${content}</embedded-editable-tag>`;
            case "select":
              const optionsStr = JSON.stringify(node.options || []).replace(/"/g, "&quot;");
              const apiConfigStr = node.apiConfig ? JSON.stringify(node.apiConfig).replace(/"/g, "&quot;") : "";
              return `<embedded-select placeholder="${node.placeholder || "Please select..."}" options="${optionsStr}" ${apiConfigStr ? `apiConfig="${apiConfigStr}"` : ""} defaultValue="${node.defaultValue || ""}" tooltips="${node.tooltips || ""}" disabled="${node.disabled || false}"></embedded-select>`;
            case "paragraph":
            default:
              const text = node.children.map((child) => (Text.isText(child) ? child.text : "")).join("");
              return text ? `<p>${text}</p>` : "";
          }
        }
        return "";
      })
      .join("");
  }, []);

  // Convert Slate value to plain text
  const serializeToText = useCallback((nodes: Descendant[]): string => {
    return nodes
      .map((node) => {
        if (SlateElement.isElement(node)) {
          switch (node.type) {
            case "tag":
              return node.rawValue || node.text || "";
            case "editable-tag":
            case "paragraph":
            default:
              return node.children.map((child) => (Text.isText(child) ? child.text : "")).join("");
            case "select":
              return node.defaultValue || "";
          }
        }
        return "";
      })
      .join("\n\n");
  }, []);

  // Handle value changes
  const handleChange = useCallback(
    (newValue: Descendant[]) => {
      setValue(newValue);
      const htmlContent = serializeToHTML(newValue);
      onChange(htmlContent);
    },
    [onChange, serializeToHTML],
  );

  // Call onChange on initialization
  useEffect(() => {
    const htmlContent = serializeToHTML(value);
    onChange(htmlContent);
  }, [onChange, serializeToHTML, value]);

  // Render custom elements
  const renderElement = useCallback((props: any) => {
    switch (props.element.type) {
      case "tag":
        return <TagElement {...props} />;
      case "embedded-editable-tag":
        return <EditableTagElement {...props} />;
      case "select":
        return <SelectElement {...props} />;
      case "paragraph":
      default:
        return <p {...props.attributes}>{props.children}</p>;
    }
  }, []);

  // Render leaf nodes (text formatting)
  const renderLeaf = useCallback((props: any) => {
    let { children } = props;

    if (props.leaf.bold) {
      children = <strong>{children}</strong>;
    }

    if (props.leaf.italic) {
      children = <em>{children}</em>;
    }

    if (props.leaf.underline) {
      children = <u>{children}</u>;
    }

    return <span {...props.attributes}>{children}</span>;
  }, []);

  // Handle key events
  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent) => {
      if (event.key === "Enter" && onEnterPress) {
        onEnterPress(event.nativeEvent);
      }
    },
    [onEnterPress],
  );

  useImperativeHandle(
    ref,
    () => ({
      focus: () => {
        ReactEditor.focus(editor);
      },
      clear: () => {
        Transforms.delete(editor, {
          at: {
            anchor: SlateEditor.start(editor, []),
            focus: SlateEditor.end(editor, []),
          },
        });
        Transforms.insertNodes(editor, {
          type: "paragraph",
          children: [{ text: "" }],
        });
        setValue([{ type: "paragraph", children: [{ text: "" }] }]);
      },
      insertText: (text: string) => {
        // Parse HTML-like text and insert appropriate elements
        if (text.includes("<embedded-tag")) {
          const match = text.match(
            /<embedded-tag text="([^"]*)" rawValue="([^"]*)" tooltips="([^"]*)"><\/embedded-tag>/,
          );
          if (match) {
            const [, tagText, rawValue, tooltips] = match;
            Transforms.insertNodes(editor, {
              type: "tag",
              text: tagText,
              rawValue: rawValue,
              tooltips: tooltips,
              children: [{ text: "" }],
            });
            return;
          }
        }

        if (text.includes("<embedded-select")) {
          const match = text.match(/<embedded-select ([^>]*)><\/embedded-select>/);
          if (match) {
            const attrs = match[1];
            const placeholder = attrs.match(/placeholder="([^"]*)"/)?.[1] || "";
            const optionsMatch = attrs.match(/options="([^"]*)"/)?.[1] || "[]";
            const options = JSON.parse(optionsMatch.replace(/&quot;/g, '"'));
            const defaultValue = attrs.match(/defaultValue="([^"]*)"/)?.[1] || "";
            const tooltips = attrs.match(/tooltips="([^"]*)"/)?.[1] || "";
            const disabled = attrs.match(/disabled="([^"]*)"/)?.[1] === "true";

            Transforms.insertNodes(editor, {
              type: "select",
              placeholder,
              options,
              defaultValue,
              tooltips,
              disabled,
              children: [{ text: "" }],
            });
            return;
          }
        }

        // Insert plain text
        Transforms.insertText(editor, text);
      },
      getText: () => {
        return serializeToText(value);
      },
    }),
    [editor, value, serializeToText],
  );

  return (
    <Slate editor={editor} initialValue={value} onValueChange={handleChange}>
      <Editable
        renderElement={renderElement}
        renderLeaf={renderLeaf}
        onKeyDown={handleKeyDown}
        style={{
          width: "100%",
          minHeight: "106px",
          fontSize: "14px",
          lineHeight: "190%",
          outline: "none",
          border: "none",
        }}
        placeholder="Type something..."
      />
    </Slate>
  );
});

export default Editor;
