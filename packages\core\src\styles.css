@import "tailwindcss" prefix(ag);

:root {
  --agent-text-dark: rgba(37, 45, 62, 0.85);
}

@utility message-loader {
  width: 30px;
  aspect-ratio: 2;
  --_g: no-repeat radial-gradient(circle closest-side, rgba(11, 104, 230, 1) 50%, #0000);
  background:
    var(--_g) 0% 50%,
    var(--_g) 50% 50%,
    var(--_g) 100% 50%;
  background-size: calc(100% / 3) 50%;
  animation: l3 1s infinite linear;
}

@utility text-dark {
  color: var(--agent-text-dark);
}

@utility text-color-65 {
  color: rgba(37, 45, 62, 0.65);
}

@utility shadow-box {
  box-shadow: 0px 9px 28px 0px rgba(0,0,0,0.05), 0px 6px 16px -8px rgba(0,0,0,0.08);
}


@keyframes l3 {
  20% {
    background-position:
      0% 0%,
      50% 50%,
      100% 50%;
  }
  40% {
    background-position:
      0% 100%,
      50% 0%,
      100% 50%;
  }
  60% {
    background-position:
      0% 50%,
      50% 100%,
      100% 0%;
  }
  80% {
    background-position:
      0% 50%,
      50% 50%,
      100% 100%;
  }
}

/*!
  Theme: GitHub Dark
  Description: Dark theme as seen on github.com
  Author: github.com
  Maintainer: @Hirse
  Updated: 2021-05-15

  Outdated base version: https://github.com/primer/github-syntax-dark
  Current colors taken from GitHub's CSS
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em;
  margin: 1em 0;
  border-radius: 6px;
}

/* 滚动条样式 */
code.hljs::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

code.hljs::-webkit-scrollbar-thumb {
  border-radius: 3px;
  background-color: rgba(255, 255, 255, 0.2);
}

code.hljs::-webkit-scrollbar-thumb:hover {
  background-color: rgba(255, 255, 255, 0.4);
}

code.hljs {
  padding: 3px 5px;
}
.hljs {
  color: #c9d1d9;
  background: #0d1117;
}
.hljs-doctag,
.hljs-keyword,
.hljs-meta .hljs-keyword,
.hljs-template-tag,
.hljs-template-variable,
.hljs-type,
.hljs-variable.language_ {
  color: #ff7b72;
}
.hljs-title,
.hljs-title.class_,
.hljs-title.class_.inherited__,
.hljs-title.function_ {
  color: #d2a8ff;
}
.hljs-attr,
.hljs-attribute,
.hljs-literal,
.hljs-meta,
.hljs-number,
.hljs-operator,
.hljs-selector-attr,
.hljs-selector-class,
.hljs-selector-id,
.hljs-variable {
  color: #79c0ff;
}
.hljs-meta .hljs-string,
.hljs-regexp,
.hljs-string {
  color: #a5d6ff;
}
.hljs-built_in,
.hljs-symbol {
  color: #ffa657;
}
.hljs-code,
.hljs-comment,
.hljs-formula {
  color: #8b949e;
}
.hljs-name,
.hljs-quote,
.hljs-selector-pseudo,
.hljs-selector-tag {
  color: #7ee787;
}
.hljs-subst {
  color: #c9d1d9;
}
.hljs-section {
  color: #1f6feb;
  font-weight: 700;
}
.hljs-bullet {
  color: #f2cc60;
}
.hljs-emphasis {
  color: #c9d1d9;
  font-style: italic;
}
.hljs-strong {
  color: #c9d1d9;
  font-weight: 700;
}
.hljs-addition {
  color: #aff5b4;
  background-color: #033a16;
}
.hljs-deletion {
  color: #ffdcd7;
  background-color: #67060c;
}

/** antdX */
.ant-conversations-label {
  color: var(--agent-text-dark) !important;
}

.ant-conversations .ant-conversations-group-title {
  padding-left: 12px !important;
}

.ant-conversations .ant-conversations-group-title .ant-typography {
  font-size: 12px !important;
}

.ant-conversations-group-title .ant-typography {
  color: rgba(37, 45, 62, 0.65) !important;
}

.ant-conversations-item:hover {
  background-color: rgba(237, 238, 239, 1) !important;
}

.ant-conversations-item-active {
  background-color: rgba(237, 238, 239, 1) !important;
}

.ant-conversations-item-active .ant-conversations-label {
  color: rgba(37, 45, 62, 0.85) !important;
}

.ant-sender-actions-btn-disabled {
  background-color: rgba(37, 45, 62, 0.25) !important;
  opacity: 1 !important;
}

.ant-sender {
  background-color: #ffffff;
}

/* markdown table */
.cscs-agent-text-block table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid #ccc;
}

.cscs-agent-text-block th,
.cscs-agent-text-block td {
  border: 1px solid #ccc;
  padding: 8px;
  text-align: left;
}

.cscs-agent-text-block th {
  background-color: #f2f2f2;
}

.cscs-agent-text-block ol {
  list-style: auto;
  padding-left: 16px;
}

.cscs-agent-text-block ul {
  list-style: initial;
  padding-left: 16px;
}

.ProseMirror {
  word-break: break-all;
}

.ProseMirror-focused {
  outline: none;
}

.cscs-agent-text-block p, .cscs-agent-text-block ul {
  margin-bottom: 4px;
}

.cscs-agent-text-block p:nth-last-child(1), .cscs-agent-text-block ul:nth-last-child(1) {
  margin-bottom: 0;
}

.div[id~="dmermaid-"] {
  display: none;
}

.cscs-agent-bubble .cscs-agent-bubble-footer-human {
  visibility: hidden;
}

.cscs-agent-bubble:hover .cscs-agent-bubble-footer-human {
  visibility: visible;
}
