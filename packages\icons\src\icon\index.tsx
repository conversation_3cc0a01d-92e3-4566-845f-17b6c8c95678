/// <reference types="vite-plugin-svgr/client" />

import React, { CSSProperties, useMemo } from "react";

import ArrowUp from "./ArrowUp.svg?react";
import Chat from "./Chat.svg?react";
import Copy from "./Copy.svg?react";
import Maximize from "./Maximize.svg?react";
import Minimize from "./Minimize.svg?react";
import SaveOutline from "./Save.svg?react";
import SearchOutline from "./Search.svg?react";
import Stop from "./Stop.svg?react";
import Tool from "./Tool.svg?react";
import WebOutline from "./Web.svg?react";

const ICON_MAP: Record<string, React.FC> = {
  WebOutline,
  SearchOutline,
  SaveOutline,
  Chat,
  ArrowUp,
  Tool,
  Stop,
  Minimize,
  Maximize,
  Copy,
};

export const Icon: React.FC<{
  icon: string;
  style?: CSSProperties;
  className?: string;
}> = (props) => {
  const { icon } = props ?? {};

  const Svg = useMemo(() => {
    if (!icon) return null;
    return ICON_MAP[icon];
  }, [icon]);

  return Svg ? (
    <span className={`cscs-agent-icon ${props.className}`} style={props.style}>
      <Svg />
    </span>
  ) : null;
};
