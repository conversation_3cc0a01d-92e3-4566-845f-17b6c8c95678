import { But<PERSON>, Toolt<PERSON>, message as antdMessage } from "antd";
import React from "react";
import { CopyToClipboard } from "react-copy-to-clipboard";

import { MessageContext } from "@cscs-agent/core";
import { Icon } from "@cscs-agent/icons";

const Copy: React.FC = () => {
  const context = React.useContext(MessageContext);

  return (
    <CopyToClipboard
      text={context?.message.getTextContent()}
      onCopy={() => {
        antdMessage.success("复制成功");
      }}
    >
      <Button
        type="text"
        size="small"
        icon={
          <span className="presets:text-color-65">
            <Tooltip title="复制">
              <Icon icon="Copy" />
            </Tooltip>
          </span>
        }
      />
    </CopyToClipboard>
  );
};

export default Copy;
