import { But<PERSON>, Modal } from "antd";
import React, { useEffect, useMemo, useRef, useState } from "react";

import { useCommandRunner, useSubscribeCommand } from "@/command";
import { useActiveAgentCode, useActiveAgentConfig, useActiveConversationId, useIsLoadingMessage } from "@/core";
import {
  BuildInCommand,
  IInsertSelectIntoSenderParams,
  IInsertTagIntoSenderParams,
  IInsertTextIntoSenderParams,
} from "@/types";
import { Icon } from "@cscs-agent/icons";

import Editor, { EditorRef } from "../editor/Editor";
import SenderHeader from "./Header";
import HeaderPanel from "./HeaderPanel";
import SendButton from "./SendButton";

// import { Icon } from "@cscs-agent/icons";

const confirm = Modal.confirm;

interface SenderProps {
  isNewConversation?: boolean;
}

const Sender: React.FC<SenderProps> = (props) => {
  const { isNewConversation = false } = props;
  const [inputValue, setInputValue] = useState("");
  const runner = useCommandRunner();
  const [activeConversationId] = useActiveConversationId();
  const [isLoadingMessage] = useIsLoadingMessage();
  const editorRef = useRef<EditorRef>(null);
  const [activeAgentCode] = useActiveAgentCode();
  const [headerPanelOpen, setHeaderPanelOpen] = useState(false);
  const activeAgentConfig = useActiveAgentConfig();

  const disabled = useMemo(() => {
    const textValue = editorRef.current?.getText().replace(/\n/g, "") ?? "";
    return textValue.trim() === "";
  }, [inputValue]);

  // 插入文字命令订阅，订阅到后，将文字插入到编辑器中。
  useSubscribeCommand(BuildInCommand.InsertTextIntoSender, (params) => {
    const { text } = params as IInsertTextIntoSenderParams;
    editorRef.current?.insertText(text);
  });

  // 插入标签命令订阅，订阅到后，将标签插入到编辑器中。
  useSubscribeCommand(BuildInCommand.InsertTagIntoSender, (params) => {
    const { text, rawValue, tooltips } = params as IInsertTagIntoSenderParams;
    const tagText = `<embedded-tag text="${text}" rawValue="${rawValue}" tooltips="${tooltips}"></embedded-tag>`;
    editorRef.current?.insertText(tagText);
  });

  // 插入选择器命令订阅，订阅到后，将选择器插入到编辑器中。
  useSubscribeCommand(BuildInCommand.InsertSelectIntoSender, (params) => {
    const { placeholder, options, defaultValue, tooltips, disabled } = params as IInsertSelectIntoSenderParams;
    const optionsStr = JSON.stringify(options).replace(/"/g, "&quot;");
    const selectText = `<embedded-select placeholder="${placeholder || "Please select..."}" options="${optionsStr}" defaultValue="${defaultValue || ""}" tooltips="${tooltips || ""}" disabled="${disabled || false}"></embedded-select>`;
    editorRef.current?.insertText(selectText);
  });

  // 关闭头部面板命令订阅
  useSubscribeCommand(BuildInCommand.CloseSenderHeaderPanel, () => {
    setHeaderPanelOpen(false);
  });

  // 路由变更时，清空编辑器，关闭头部面板
  useEffect(() => {
    editorRef.current?.clear();
    setHeaderPanelOpen(false);
    setInputValue("");
  }, [activeConversationId, activeAgentCode]);

  const onSubmit = () => {
    const message = editorRef.current?.getText() ?? "";
    setInputValue("");
    if (isNewConversation) {
      runner(BuildInCommand.SendMessage, { message, agentCode: activeAgentCode, isNewConversation: true });
    } else {
      runner(BuildInCommand.SendMessage, { message, conversationId: activeConversationId, agentCode: activeAgentCode });
    }
    editorRef.current?.clear();
  };

  const cancel = () => {
    confirm({
      title: "取消发送",
      content: "确定要取消当前对话吗？",
      onOk() {
        runner(BuildInCommand.CancelChatRequest);
      },
    });
  };

  const handleEnterPress = (event: KeyboardEvent) => {
    // 如果是Ctrl+Enter，允许换行，不提交
    if (event.ctrlKey) {
      return;
    }

    // 如果是普通Enter键，阻止默认行为并提交消息
    event.preventDefault();

    // 检查是否有内容可以提交
    const message = editorRef.current?.getText() ?? "";
    if (message.trim() !== "" && !isLoadingMessage) {
      onSubmit();
    }
  };

  const enableHeaderPanel = activeAgentConfig?.sender?.headerPanel?.enable ?? true;

  return (
    <>
      <HeaderPanel open={headerPanelOpen} onClose={() => setHeaderPanelOpen(false)} />
      <SenderHeader />
      <div className="ag:flex ag:p-4 ag:rounded-xl ag:bg-white ag:border ag:border-gray-200 ag:shadow-sm ag:items-stretch">
        <div className="ag:flex-1">
          <Editor onChange={setInputValue} onEnterPress={handleEnterPress} ref={editorRef} />
        </div>
        <div className="ag:flex ag:flex-col ag:ml-2 ag:w-[52px] ag:justify-between ag:items-end">
          <div>
            <Button
              icon={headerPanelOpen ? <Icon icon="Minimize" /> : <Icon icon="Maximize" />}
              onClick={() => setHeaderPanelOpen(!headerPanelOpen)}
              type="text"
              size="small"
              hidden={!enableHeaderPanel}
            >
              <span className="ag:text-color-65">模板</span>
            </Button>
          </div>
          <SendButton onClick={() => onSubmit()} onCancel={cancel} isLoading={isLoadingMessage} disabled={disabled} />
        </div>
      </div>
    </>
  );
};

export default Sender;
